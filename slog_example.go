package main

import (
	"log/slog"
	"os"
	"time"
)

// Example demonstrating slog usage with reconnect functionality
func slogExample() {
	// Create different types of loggers
	
	// 1. Text logger (default)
	textLogger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))
	
	// 2. JSON logger for structured logging
	jsonLogger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))
	
	// 3. Custom logger with additional context
	contextLogger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	})).With(
		slog.String("component", "websocket_client"),
		slog.String("version", "1.0.0"),
	)

	var cookie = "your_cookie_here"
	client, err := New("https://www.moyu-idle.com", cookie)
	if err != nil {
		slog.Error("创建客户端失败", slog.String("error", err.Error()))
		return
	}

	// Example 1: Using text logger
	slog.Info("使用文本日志格式")
	client.SetLogger(textLogger)
	client.EnableAutoReconnect(3*time.Second, 5)

	// Example 2: Using JSON logger
	slog.Info("切换到JSON日志格式")
	client.SetLogger(jsonLogger)
	
	// Example 3: Using context logger
	slog.Info("使用带上下文的日志")
	client.SetLogger(contextLogger)

	// Set up event handler with structured logging
	client.OnEventHandle = func(event string, val []any) {
		contextLogger.Info("📨 收到游戏事件", 
			slog.String("event", event),
			slog.Int("data_count", len(val)),
			slog.String("action", "game_event"))
		
		// Handle specific events with structured logging
		switch event {
		case "battleRoom:join:success":
			contextLogger.Info("✅ 成功加入战斗房间", 
				slog.String("event", event),
				slog.String("status", "success"))
		case "battleRoom:update":
			contextLogger.Info("🔄 战斗房间状态更新", 
				slog.String("event", event),
				slog.String("status", "updated"))
		case "battle:end:success":
			contextLogger.Info("🏆 战斗结束", 
				slog.String("event", event),
				slog.String("status", "completed"))
		}
	}

	// Connect to the server
	if err := client.Connect(); err != nil {
		contextLogger.Error("❌ 连接失败", 
			slog.String("error", err.Error()),
			slog.String("action", "connect_failed"))
		return
	}

	// Wait for connection to be established
	contextLogger.Info("⏳ 等待连接建立...")
	for i := 0; i < 100; i++ {
		if client.IsConnected() {
			contextLogger.Info("✅ 连接已建立", 
				slog.String("status", "connected"))
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	// Demonstrate different log levels
	contextLogger.Debug("这是调试信息", slog.String("level", "debug"))
	contextLogger.Info("这是信息日志", slog.String("level", "info"))
	contextLogger.Warn("这是警告日志", slog.String("level", "warn"))
	contextLogger.Error("这是错误日志", slog.String("level", "error"))

	// Simulate running for a while
	contextLogger.Info("🎮 客户端运行中...", 
		slog.String("action", "running"))
	time.Sleep(10 * time.Second)

	// Test manual reconnect with logging
	contextLogger.Info("🔄 测试手动重连...")
	if err := client.Reconnect(); err != nil {
		contextLogger.Error("❌ 手动重连失败", 
			slog.String("error", err.Error()),
			slog.String("action", "manual_reconnect_failed"))
	}

	// Run for a bit more
	time.Sleep(5 * time.Second)

	// Disable auto-reconnect
	client.DisableAutoReconnect()

	// Close the connection
	contextLogger.Info("🔌 关闭连接...")
	if err := client.Close(); err != nil {
		contextLogger.Error("❌ 关闭连接失败", 
			slog.String("error", err.Error()),
			slog.String("action", "close_failed"))
	}

	contextLogger.Info("✅ 示例完成", 
		slog.String("status", "completed"))
}

// Example of setting up different log levels
func setupLogging() {
	// Debug level - shows all logs
	debugLogger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))
	
	// Info level - shows info, warn, error
	infoLogger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))
	
	// Warn level - shows warn, error only
	warnLogger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelWarn,
	}))
	
	// Error level - shows error only
	errorLogger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))
	
	// Set as default
	slog.SetDefault(debugLogger)
	
	// Use different loggers as needed
	_ = infoLogger
	_ = warnLogger
	_ = errorLogger
}
