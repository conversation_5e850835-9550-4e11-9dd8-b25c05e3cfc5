package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
)

func (c *Client) getSession(baseUrl, cookie string) (*UserSessionResp, error) {
	// 创建HTTP客户端
	client := &http.Client{}

	// 创建GET请求
	req, err := http.NewRequest("GET", fmt.Sprintf("%s/api/_auth/session", baseUrl), nil)
	if err != nil {
		c.logger.Error("创建请求失败",
			slog.String("error", err.Error()),
			slog.String("url", baseUrl))
		return nil, err
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":  []string{"application/json"}, // 注意这里是application/json
		"Cookie":  []string{cookie},
		"Referer": []string{baseUrl},
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	var userSessionResp UserSessionResp
	if err := json.Unmarshal(body, &userSessionResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}
	return &userSessionResp, nil
}

type UserSessionResp struct {
	User User   `json:"user"`
	ID   string `json:"id"`
}

type User struct {
	Uuid               string `json:"uuid"`
	Name               string `json:"name"`
	IsVisitor          bool   `json:"isVisitor"`
	FirstResetPassword bool   `json:"firstResetPassword"`
	IsGm               bool   `json:"isGm"`
	VisitorPwd         string `json:"visitorPwd"`
}
